import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template4 = ({ data }) => {
  const { billTo = {}, shipTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-4xl font-bold text-purple-600 mb-4">{translations.invoice}</h1>
            <p>
              <span className="font-semibold">Númer<PERSON> da Fatura:</span>{" "}
              {invoice.number || "N/A"}
            </p>
            <p>
              <span className="font-semibold">Data da Fatura:</span>{" "}
              {invoice.date
                ? formatDateBR(invoice.date)
                : "N/A"}
            </p>
            <p>
              <span className="font-semibold">Data de Vencimento:</span>{" "}
              {invoice.paymentDate
                ? formatDateBR(invoice.paymentDate)
                : "N/A"}
            </p>
          </div>
          <div className="text-right">
            <h2 className="text-2xl font-bold">
              {yourCompany.name || "Nome da Empresa"}
            </h2>
            <p>{yourCompany.address || "Endereço da Empresa"}</p>
            <p>{yourCompany.phone || "Telefone da Empresa"}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-8">
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="text-lg font-semibold text-purple-600 mb-2">
              Faturado por
            </h3>
            <p>
              <strong>{yourCompany.name || "Nome da Empresa"}</strong>
            </p>
            <p>{yourCompany.address || "Endereço da Empresa"}</p>
            <p>{yourCompany.phone || "Telefone da Empresa"}</p>
          </div>
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="text-lg font-semibold text-purple-600 mb-2">
              Faturado para
            </h3>
            <p>
              <strong>{billTo.name || "Nome do Cliente"}</strong>
            </p>
            <p>{billTo.address || "Endereço do Cliente"}</p>
            <p>{billTo.phone || "Telefone do Cliente"}</p>
          </div>
        </div>

        <table className="w-full mb-8 border border-gray-300">
          <thead className="bg-purple-600 text-white">
            <tr>
              <th className="p-2 text-left border border-gray-300">
                Item #/Descrição do Item
              </th>
              <th className="p-2 text-right border border-gray-300">Qtde.</th>
              <th className="p-2 text-right border border-gray-300">Preço Unit.</th>
              <th className="p-2 text-right border border-gray-300">{translations.amount}</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index} className="bg-gray-100">
                <td className="p-2 border border-gray-300">
                  {`${index + 1}. ${item.name || "Nome do Item"}`}
                  <br />
                  <span className="text-sm text-gray-600">
                    {item.description || "Descrição do Item"}
                  </span>
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {item.quantity || 0}
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {formatCurrency(item.amount || 0, selectedCurrency)}
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {formatCurrency((item.quantity || 0) * (item.amount || 0), selectedCurrency)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="flex justify-end mb-8">
          <div className="w-1/3">
            <p className="flex justify-between">
              <span>{translations.subtotal}:</span> <span>{formatCurrency(subTotal, selectedCurrency)}</span>
            </p>
            {taxPercentage > 0 && (
              <>
                <p className="flex justify-between">
                  <span>Imposto({taxPercentage}%):</span> <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
                </p>
              </>
            )}
            <hr className="my-2" />
            <p className="flex justify-between font-bold text-lg mt-2">
              <span>{translations.total}:</span> <span>{formatCurrency(grandTotal, selectedCurrency)}</span>
            </p>
          </div>
        </div>

        {notes && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-purple-600 mb-2">{translations.notes}</h3>
            <p>{notes}</p>
          </div>
        )}
        
        <div className="flex justify-center mb-8">
          <PixQRCode 
            pixData={pixData}
            grandTotal={grandTotal}
            size={150}
            className="mt-4"
          />
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template4;
