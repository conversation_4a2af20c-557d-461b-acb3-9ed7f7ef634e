import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template6 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h2 className="text-2xl font-bold" style={{ color: "#14A8DE" }}>
              {yourCompany.name || "Nome da Empresa"}
            </h2>
            <p>{yourCompany.address || "Endereço da Empresa"}</p>
            <p>{yourCompany.phone || "Telefone da Empresa"}</p>
          </div>
          <div className="text-right">
            <h1 className="text-3xl font-thin mb-4">{translations.invoice}</h1>
            <p>
              <span className="font-semibold">Número da Fatura:</span>{" "}
              {invoice.number || "N/A"}
            </p>
            <p>
              <span className="font-semibold">Data da Fatura:</span>{" "}
              {invoice.date
                ? formatDateBR(invoice.date)
                : "N/A"}
            </p>
            <p>
              <span className="font-semibold">Data de Vencimento:</span>{" "}
              {invoice.paymentDate
                ? formatDateBR(invoice.paymentDate)
                : "N/A"}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold mb-2">Faturado para</h3>
            <p>{billTo.name || "Nome do Cliente"}</p>
            <p>{billTo.address || "Endereço do Cliente"}</p>
          </div>
        </div>

        <table className="w-full mb-8 border border-gray-300">
          <thead style={{ backgroundColor: "#14A8DE" }}>
            <tr>
              <th className="p-2 text-left border-b border-gray-300 text-white">
                Item #/Descrição do Item
              </th>
              <th className="p-2 text-right border-b border-gray-300 text-white">
                {translations.quantity}
              </th>
              <th className="p-2 text-right border-b border-gray-300 text-white">
                Preço Unit.
              </th>
              <th className="p-2 text-right border-b border-gray-300 text-white">
                {translations.amount}
              </th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index}>
                <td className="p-2 border border-gray-300">
                  <p className="font-semibold">{item.name || "Nome do Item"}</p>
                  <p className="text-sm text-gray-600">
                    {item.description || "Descrição do Item"}
                  </p>
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {item.quantity || 0}
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {formatCurrency(item.amount || 0, selectedCurrency)}
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {formatCurrency((item.amount || 0) * (item.quantity || 0), selectedCurrency)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="flex justify-end">
          <table className="w-1/2 mb-8 border border-gray-300">
            <tbody>
              <tr>
                <td className="p-2 text-right font-semibold border border-gray-300">
                  {translations.subtotal}
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {formatCurrency(subTotal, selectedCurrency)}
                </td>
              </tr>
              {taxPercentage > 0 && (
                <tr>
                  <td className="p-2 text-right font-semibold border border-gray-300">
                    Imposto ({taxPercentage}%)
                  </td>
                  <td className="p-2 text-right border border-gray-300">
                    {formatCurrency(taxAmount, selectedCurrency)}
                  </td>
                </tr>
              )}
              <tr className="text-white" style={{ backgroundColor: "#14A8DE" }}>
                <td className="p-2 text-right font-semibold border border-gray-300">
                  Valor Total a Pagar
                </td>
                <td className="p-2 text-right border border-gray-300">
                  {formatCurrency(grandTotal, selectedCurrency)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className="text-center text-sm border-t pt-4">
          <p>{notes}</p>
          <div className="flex justify-center mt-4">
            <PixQRCode 
              pixData={pixData}
              grandTotal={grandTotal}
              size={130}
              className="mt-2"
            />
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template6;
