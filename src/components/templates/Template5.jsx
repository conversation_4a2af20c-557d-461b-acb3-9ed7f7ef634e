import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template5 = ({ data = {} }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData } = data;

  return (
    <BaseTemplate data={data}>
      <div className="bg-white max-w-4xl mx-auto flex flex-col h-full overflow-hidden">
        <div className="p-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-4xl font-bold text-green-600">{translations.invoice}</h1>
            </div>
            <div className="text-right">
              <h2 className="text-xl font-bold">
                {yourCompany.name || "Nome da Empresa"}
              </h2>
              <p>{yourCompany.address || "Endereço da Empresa"}</p>
              <p>{yourCompany.phone || "Telefone da Empresa"}</p>
            </div>
          </div>

          <div className="flex justify-between mb-8 mt-4">
            <div className="text-left w-1/2">
              <h3 className="text-lg font-semibold text-green-600 mb-2">
                Faturado para
              </h3>
              <p className="font-bold">{billTo.name || "Nome do Cliente"}</p>
              <p>{billTo.address || "Endereço do Cliente"}</p>
              <p>{billTo.phone || "Telefone do Cliente"}</p>
            </div>
            <div className="text-right w-1/3">
              <h3 className="text-lg font-semibold text-green-600 mb-2 text-left">
                Detalhes da Fatura
              </h3>
              <p className="flex justify-between">
                <span className="font-semibold">Número da Fatura:</span>
                <span>{invoice.number || "N/A"}</span>
              </p>
              <p className="flex justify-between">
                <span className="font-semibold">Data da Fatura:</span>
                <span>
                  {invoice.date
                    ? formatDateBR(invoice.date)
                    : "N/A"}
                </span>
              </p>
              <p className="flex justify-between">
                <span className="font-semibold">Data de Vencimento:</span>
                <span>
                  {invoice.paymentDate
                    ? formatDateBR(invoice.paymentDate)
                    : "N/A"}
                </span>
              </p>
            </div>
          </div>

          <table className="w-full mb-8 border border-green-600">
            <thead className="bg-green-600 text-white">
              <tr>
                <th className="p-2 text-left">Item #/Descrição do Item</th>
                <th className="p-2 text-right">Qtde.</th>
                <th className="p-2 text-right">Preço Unit.</th>
                <th className="p-2 text-right">{translations.amount}</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-green-50" : ""}
                >
                  <td className="p-2">{item.name || "Nome do Item"}</td>
                  <td className="p-2 text-right">{item.quantity || 0}</td>
                  <td className="p-2 text-right">
                    {formatCurrency(item.amount || 0, selectedCurrency)}
                  </td>
                  <td className="p-2 text-right">
                    {formatCurrency((item.quantity || 0) * (item.amount || 0), selectedCurrency)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="flex justify-end mb-8">
            <div className="w-1/3">
              <p className="flex justify-between">
                <span>{translations.subtotal}:</span> <span>{formatCurrency(subTotal, selectedCurrency)}</span>
              </p>
              {taxPercentage > 0 && (
                <p className="flex justify-between">
                  <span>Imposto ({taxPercentage}%):</span>{" "}
                  <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
                </p>
              )}
              <p className="flex justify-between font-bold text-lg mt-2">
                <span>Total a Pagar:</span>{" "}
                <span className="text-green-600">
                  {formatCurrency(grandTotal, selectedCurrency)}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div className="flex-grow bg-green-50 overflow-auto">
          {notes && (
            <div className="p-4">
              <h3 className="text-lg font-semibold text-green-600 mb-2">
                {translations.notes}
              </h3>
              <p>{notes}</p>
            </div>
          )}
          <div className="p-4 flex justify-center">
            <PixQRCode 
              pixData={pixData}
              grandTotal={grandTotal}
              size={140}
              className="mt-2"
            />
          </div>
        </div>
        <div className="p-4 text-center text-sm text-gray-600 bg-green-50">
          Esta é uma fatura gerada por computador e não requer assinatura.
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template5;
